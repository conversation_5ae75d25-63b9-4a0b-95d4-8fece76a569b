package com.inspeedia.vanning.service;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.dto.TaskDto;

import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class TaskConverter {

    private final AlertService alertService;

    public TaskConverter(AlertService alertService) {
        this.alertService = alertService;
    }

    public TaskDto convertToTaskDto(PlannedWork plannedWork, ActualWork actualWork) {
        TaskDto task = new TaskDto();

        // Set basic properties
        task.setId(plannedWork.getId());
        task.setRowName(plannedWork.getRowName());
        task.setName(plannedWork.getOperatorName());
        task.setShippingDate(plannedWork.getWorkDate().toString());
        task.setVanGp(plannedWork.getVanGp());
        task.setDeliveryTime(formatTime(plannedWork.getLoadTime()));
        task.setPlannedStart(formatTime(plannedWork.getStartTime()));
        task.setPlannedEnd(formatTime(plannedWork.getEndTime()));
        task.setPlannedDuration(formatDuration(plannedWork.getDuration()));

        // Set actual work data if available
        if (actualWork != null) {
            task.setActualStart(formatTime(actualWork.getStartTime()));
            task.setActualEnd(formatTime(actualWork.getEndTime()));
            task.setActualDuration(formatDuration(actualWork.getDuration()));
            Integer progress = actualWork.getProgress();
            Float progressRate = actualWork.getProgressRate();
            task.setProgress(progress != null ? progress : 0);
            task.setProgressRate(progressRate);
            task.setAlerts(alertService.calculateAlerts(plannedWork, actualWork));
        } else {
            task.setActualStart("");
            task.setActualEnd("");
            task.setActualDuration("");
            task.setProgress(0);
            task.setProgressRate(null);
            task.setAlerts(List.of());
        }

        return task;
    }

    /**
     * Format LocalTime to string (HH:mm format)
     */
    private String formatTime(LocalTime time) {
        if (time == null) {
            return "";
        }
        return time.format(DateTimeFormatter.ofPattern("HH:mm"));
    }

    /**
     * Format duration LocalTime to readable string
     */
    private String formatDuration(LocalTime duration) {
        if (duration == null) {
            return "";
        }
        int hours = duration.getHour();
        int minutes = duration.getMinute();

        if (minutes == 0) {
            return hours + "h";
        } else {
            return String.format("%dh %dm", hours, minutes);
        }
    }
}
